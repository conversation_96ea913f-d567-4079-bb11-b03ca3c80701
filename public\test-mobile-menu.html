<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة المحمولة</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <a href="index.html" class="logo">تمسيك</a>
                <p class="slogan">"والذين يمسكون بالكتاب..."</p>
                <button type="button" class="mobile-menu-toggle" aria-label="فتح القائمة" title="فتح القائمة">
                    <i class="fas fa-bars"></i>
                </button>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sermons.html"><i class="fas fa-book-open"></i> الخطب الجاهزة</a></li>
                    <li><a href="prepare_sermon.html" class="active"><i class="fas fa-pen"></i> إعداد خطبة</a></li>
                    <li><a href="scholars.html"><i class="fas fa-user-graduate"></i> العلماء اليمنيين</a></li>
                    <li><a href="thinkers.html"><i class="fas fa-lightbulb"></i> المفكرون والدعاة</a></li>
                    <li><a href="lectures.html"><i class="fas fa-microphone"></i> المحاضرات والدروس</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main style="padding: 50px;">
        <h1>اختبار القائمة المحمولة</h1>
        <p>قم بتصغير النافذة واختبار زر القائمة</p>
        <button onclick="testMenu()">اختبار القائمة برمجياً</button>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل صفحة الاختبار');
            
            // تفعيل القائمة المنسدلة للأجهزة الصغيرة
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navLinks = document.querySelector('.nav-links');

            console.log('زر القائمة:', mobileMenuToggle);
            console.log('روابط القائمة:', navLinks);

            if (mobileMenuToggle && navLinks) {
                console.log('تم العثور على عناصر القائمة، إضافة مستمعي الأحداث...');
                
                mobileMenuToggle.addEventListener('click', function() {
                    console.log('تم النقر على زر القائمة');
                    navLinks.classList.toggle('active');
                    console.log('حالة القائمة:', navLinks.classList.contains('active') ? 'مفتوحة' : 'مغلقة');
                });

                // إغلاق القائمة عند النقر على أي رابط
                const navItems = document.querySelectorAll('.nav-links a');
                navItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (window.innerWidth <= 768) {
                            navLinks.classList.remove('active');
                        }
                    });
                });

                // إعادة ضبط القائمة عند تغيير حجم النافذة
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        navLinks.classList.remove('active');
                    }
                });
            } else {
                console.error('لم يتم العثور على عناصر القائمة المحمولة');
                if (!mobileMenuToggle) console.error('لم يتم العثور على زر القائمة (.mobile-menu-toggle)');
                if (!navLinks) console.error('لم يتم العثور على روابط القائمة (.nav-links)');
            }
        });

        function testMenu() {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('active');
            console.log('تم تبديل حالة القائمة برمجياً');
        }
    </script>
</body>
</html>
